import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { authService } from '@/services';
import { useAuthStore } from '@/store';
import { useNotificationActions } from '@/store/notificationStore';
import { 
  LoginRequest, 
  CreateUserRequest, 
  ChangePasswordRequest,
  UpdateUserRequest,
  MutationOptions
} from '@/types';

// Query keys
export const authKeys = {
  all: ['auth'] as const,
  profile: () => [...authKeys.all, 'profile'] as const,
  me: () => [...authKeys.all, 'me'] as const
};

// Get current user profile
export const useProfile = () => {
  const { isAuthenticated } = useAuthStore();
  
  return useQuery({
    queryKey: authKeys.profile(),
    queryFn: authService.getProfile,
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false
  });
};

// Get current user info (lightweight)
export const useMe = () => {
  const { isAuthenticated } = useAuthStore();
  
  return useQuery({
    queryKey: authKeys.me(),
    queryFn: authService.me,
    enabled: isAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: false
  });
};

// Login mutation
export const useLogin = (options?: MutationOptions) => {
  const { setAuth } = useAuthStore();
  const notifications = useNotificationActions();
  
  return useMutation({
    mutationFn: (credentials: LoginRequest) => authService.login(credentials),
    onSuccess: (data) => {
      setAuth(data.user, data.token, data.refreshToken);
      notifications.success('Welcome back!', 'You have been logged in successfully.');
      options?.onSuccess?.(data, credentials);
    },
    onError: (error: any) => {
      notifications.error('Login Failed', error.error || 'Invalid credentials');
      options?.onError?.(error, credentials);
    },
    onSettled: options?.onSettled
  });
};

// Register mutation
export const useRegister = (options?: MutationOptions) => {
  const { setAuth } = useAuthStore();
  const notifications = useNotificationActions();
  
  return useMutation({
    mutationFn: (userData: CreateUserRequest) => authService.register(userData),
    onSuccess: (data) => {
      setAuth(data.user, data.token, data.refreshToken);
      notifications.success('Welcome!', 'Your account has been created successfully.');
      options?.onSuccess?.(data, userData);
    },
    onError: (error: any) => {
      notifications.error('Registration Failed', error.error || 'Failed to create account');
      options?.onError?.(error, userData);
    },
    onSettled: options?.onSettled
  });
};

// Logout mutation
export const useLogout = (options?: MutationOptions) => {
  const { clearAuth } = useAuthStore();
  const queryClient = useQueryClient();
  const notifications = useNotificationActions();
  
  return useMutation({
    mutationFn: authService.logout,
    onSuccess: () => {
      clearAuth();
      queryClient.clear(); // Clear all cached data
      notifications.info('Goodbye!', 'You have been logged out successfully.');
      options?.onSuccess?.();
    },
    onError: (error: any) => {
      // Even if logout fails on server, clear local state
      clearAuth();
      queryClient.clear();
      notifications.warning('Logout', 'You have been logged out locally.');
      options?.onError?.(error);
    },
    onSettled: options?.onSettled
  });
};

// Update profile mutation
export const useUpdateProfile = (options?: MutationOptions) => {
  const { updateUser } = useAuthStore();
  const queryClient = useQueryClient();
  const notifications = useNotificationActions();
  
  return useMutation({
    mutationFn: (userData: UpdateUserRequest) => authService.updateProfile(userData),
    onSuccess: (data) => {
      updateUser(data);
      queryClient.invalidateQueries({ queryKey: authKeys.profile() });
      queryClient.invalidateQueries({ queryKey: authKeys.me() });
      notifications.success('Profile Updated', 'Your profile has been updated successfully.');
      options?.onSuccess?.(data, userData);
    },
    onError: (error: any) => {
      notifications.error('Update Failed', error.error || 'Failed to update profile');
      options?.onError?.(error, userData);
    },
    onSettled: options?.onSettled
  });
};

// Change password mutation
export const useChangePassword = (options?: MutationOptions) => {
  const notifications = useNotificationActions();
  
  return useMutation({
    mutationFn: (passwordData: ChangePasswordRequest) => authService.changePassword(passwordData),
    onSuccess: () => {
      notifications.success('Password Changed', 'Your password has been updated successfully.');
      options?.onSuccess?.();
    },
    onError: (error: any) => {
      notifications.error('Password Change Failed', error.error || 'Failed to change password');
      options?.onError?.(error);
    },
    onSettled: options?.onSettled
  });
};
