import { api } from '@/utils/api';
import { API_ENDPOINTS } from '@/utils/config';
import { 
  Event, 
  CreateEventRequest, 
  UpdateEventRequest,
  EventSearchParams,
  PaginatedEventsResponse
} from '@/types';

export const eventService = {
  // Get events with filters and pagination
  getEvents: async (params: EventSearchParams): Promise<PaginatedEventsResponse> => {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    
    if (params.filters) {
      const { filters } = params;
      if (filters.startDate) queryParams.append('startDate', filters.startDate.toISOString());
      if (filters.endDate) queryParams.append('endDate', filters.endDate.toISOString());
      if (filters.type?.length) queryParams.append('type', filters.type.join(','));
      if (filters.status?.length) queryParams.append('status', filters.status.join(','));
      if (filters.priority?.length) queryParams.append('priority', filters.priority.join(','));
      if (filters.tags?.length) queryParams.append('tags', filters.tags.join(','));
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.createdBy) queryParams.append('createdBy', filters.createdBy);
      if (filters.location) queryParams.append('location', filters.location);
    }

    const response = await api.get<Event[]>(`${API_ENDPOINTS.EVENTS.BASE}?${queryParams}`);
    
    // Transform response to match PaginatedEventsResponse
    return {
      events: response.data!,
      total: response.data!.length, // This should come from pagination meta in real response
      page: params.page || 1,
      limit: params.limit || 20,
      totalPages: Math.ceil((response.data!.length) / (params.limit || 20))
    };
  },

  // Get event by ID
  getEvent: async (id: string): Promise<Event> => {
    const response = await api.get<Event>(API_ENDPOINTS.EVENTS.BY_ID(id));
    return response.data!;
  },

  // Create new event
  createEvent: async (eventData: CreateEventRequest): Promise<Event> => {
    const response = await api.post<Event>(API_ENDPOINTS.EVENTS.BASE, eventData);
    return response.data!;
  },

  // Update event
  updateEvent: async (id: string, eventData: UpdateEventRequest): Promise<Event> => {
    const response = await api.put<Event>(API_ENDPOINTS.EVENTS.BY_ID(id), eventData);
    return response.data!;
  },

  // Delete event
  deleteEvent: async (id: string): Promise<void> => {
    await api.delete(API_ENDPOINTS.EVENTS.BY_ID(id));
  },

  // Get events by date range (for calendar view)
  getEventsByDateRange: async (startDate: Date, endDate: Date): Promise<Event[]> => {
    const queryParams = new URLSearchParams({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    });

    const response = await api.get<Event[]>(`${API_ENDPOINTS.EVENTS.CALENDAR}?${queryParams}`);
    return response.data!;
  },

  // Get upcoming events
  getUpcomingEvents: async (limit: number = 10): Promise<Event[]> => {
    const queryParams = new URLSearchParams({
      limit: limit.toString()
    });

    const response = await api.get<Event[]>(`${API_ENDPOINTS.EVENTS.UPCOMING}?${queryParams}`);
    return response.data!;
  },

  // Search events
  searchEvents: async (query: string, limit: number = 20): Promise<Event[]> => {
    const queryParams = new URLSearchParams({
      q: query,
      limit: limit.toString()
    });

    const response = await api.get<Event[]>(`${API_ENDPOINTS.EVENTS.SEARCH}?${queryParams}`);
    return response.data!;
  },

  // Get event statistics
  getEventStats: async (): Promise<{
    total: number;
    published: number;
    draft: number;
    upcoming: number;
    ongoing: number;
    completed: number;
  }> => {
    const response = await api.get<{
      total: number;
      published: number;
      draft: number;
      upcoming: number;
      ongoing: number;
      completed: number;
    }>(API_ENDPOINTS.EVENTS.STATS);
    return response.data!;
  }
};
