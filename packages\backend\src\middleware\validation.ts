import { Request, Response, NextFunction } from 'express';
import { validationResult, Validation<PERSON>hain } from 'express-validator';
import { z } from 'zod';
import { logger } from '../utils/logger';

// Middleware to handle express-validator results
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors: Record<string, string[]> = {};
    
    errors.array().forEach(error => {
      const field = error.type === 'field' ? error.path : 'general';
      if (!formattedErrors[field]) {
        formattedErrors[field] = [];
      }
      formattedErrors[field].push(error.msg);
    });

    res.status(400).json({
      success: false,
      error: 'Validation failed',
      errors: formattedErrors
    });
    return;
  }

  next();
};

// Generic Zod validation middleware
export const validateSchema = <T>(schema: z.ZodSchema<T>, property: 'body' | 'query' | 'params' = 'body') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const data = req[property];
      const validatedData = schema.parse(data);
      
      // Replace the original data with validated data
      req[property] = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formattedErrors: Record<string, string[]> = {};
        
        error.errors.forEach(err => {
          const path = err.path.join('.');
          if (!formattedErrors[path]) {
            formattedErrors[path] = [];
          }
          formattedErrors[path].push(err.message);
        });

        res.status(400).json({
          success: false,
          error: 'Validation failed',
          errors: formattedErrors
        });
        return;
      }

      logger.error('Validation middleware error', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  };
};

// Middleware to validate pagination parameters
export const validatePagination = (req: Request, res: Response, next: NextFunction): void => {
  const { page, limit } = req.query;

  // Validate page
  if (page !== undefined) {
    const pageNum = parseInt(page as string, 10);
    if (isNaN(pageNum) || pageNum < 1) {
      res.status(400).json({
        success: false,
        error: 'Page must be a positive integer'
      });
      return;
    }
    req.query.page = pageNum.toString();
  }

  // Validate limit
  if (limit !== undefined) {
    const limitNum = parseInt(limit as string, 10);
    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      res.status(400).json({
        success: false,
        error: 'Limit must be between 1 and 100'
      });
      return;
    }
    req.query.limit = limitNum.toString();
  }

  next();
};

// Middleware to validate date range parameters
export const validateDateRange = (req: Request, res: Response, next: NextFunction): void => {
  const { startDate, endDate } = req.query;

  if (startDate) {
    const start = new Date(startDate as string);
    if (isNaN(start.getTime())) {
      res.status(400).json({
        success: false,
        error: 'Invalid start date format'
      });
      return;
    }
    req.query.startDate = start.toISOString();
  }

  if (endDate) {
    const end = new Date(endDate as string);
    if (isNaN(end.getTime())) {
      res.status(400).json({
        success: false,
        error: 'Invalid end date format'
      });
      return;
    }
    req.query.endDate = end.toISOString();
  }

  // Validate that start date is before end date
  if (startDate && endDate) {
    const start = new Date(req.query.startDate as string);
    const end = new Date(req.query.endDate as string);
    
    if (start >= end) {
      res.status(400).json({
        success: false,
        error: 'Start date must be before end date'
      });
      return;
    }
  }

  next();
};

// Middleware to validate MongoDB ObjectId
export const validateObjectId = (paramName: string = 'id') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const id = req.params[paramName];
    
    if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
      res.status(400).json({
        success: false,
        error: `Invalid ${paramName} format`
      });
      return;
    }

    next();
  };
};

// Middleware to sanitize input data
export const sanitizeInput = (req: Request, res: Response, next: NextFunction): void => {
  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      return obj.trim().replace(/[<>]/g, '');
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          sanitized[key] = sanitizeObject(obj[key]);
        }
      }
      return sanitized;
    }
    
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }

  next();
};

// Middleware to validate file upload
export const validateFileUpload = (allowedTypes: string[], maxSize: number) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.file) {
      res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
      return;
    }

    // Check file type
    if (!allowedTypes.includes(req.file.mimetype)) {
      res.status(400).json({
        success: false,
        error: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
      });
      return;
    }

    // Check file size
    if (req.file.size > maxSize) {
      res.status(400).json({
        success: false,
        error: `File size too large. Maximum size: ${maxSize / (1024 * 1024)}MB`
      });
      return;
    }

    next();
  };
};
