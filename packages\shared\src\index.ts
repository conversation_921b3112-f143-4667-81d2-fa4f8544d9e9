// Export all types
export * from './types/event';
export * from './types/user';
export * from './types/api';

// Export all utilities
export * from './utils/date';
export * from './utils/validation';

// Export constants
export const APP_NAME = 'GBF Events Calendar';
export const APP_VERSION = '1.0.0';

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;

// File upload constraints
export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
export const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'image/jpeg',
  'image/png',
  'image/gif'
];

// Date format constants
export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  INPUT: 'yyyy-MM-dd',
  TIME: 'HH:mm',
  DATETIME: 'MMM dd, yyyy HH:mm'
} as const;

// Color scheme for event types
export const EVENT_TYPE_COLORS = {
  meeting: '#1890ff',
  workshop: '#52c41a',
  conference: '#722ed1',
  training: '#fa8c16',
  social: '#eb2f96',
  announcement: '#13c2c2',
  other: '#8c8c8c'
} as const;

// Priority colors
export const PRIORITY_COLORS = {
  low: '#52c41a',
  medium: '#faad14',
  high: '#fa8c16',
  urgent: '#ff4d4f'
} as const;

// Status colors
export const STATUS_COLORS = {
  draft: '#8c8c8c',
  published: '#52c41a',
  cancelled: '#ff4d4f',
  completed: '#1890ff'
} as const;
