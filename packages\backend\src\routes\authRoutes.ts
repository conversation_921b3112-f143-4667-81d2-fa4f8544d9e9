import { Router } from 'express';
import { authController } from '../controllers/authController';
import { authenticate } from '../middleware/auth';
import { validateSchema, sanitizeInput } from '../middleware/validation';
import { 
  LoginSchema, 
  CreateUserSchema, 
  ChangePasswordSchema,
  UpdateUserSchema 
} from '@gbf-calendar/shared';

const router = Router();

// Public routes
router.post('/login', 
  sanitizeInput,
  validateSchema(LoginSchema),
  authController.login
);

router.post('/register', 
  sanitizeInput,
  validateSchema(CreateUserSchema),
  authController.register
);

router.post('/refresh', 
  sanitizeInput,
  authController.refreshToken
);

// Protected routes
router.use(authenticate); // All routes below require authentication

router.post('/logout', authController.logout);

router.get('/profile', authController.getProfile);

router.put('/profile', 
  sanitizeInput,
  validateSchema(UpdateUserSchema),
  authController.updateProfile
);

router.post('/change-password', 
  sanitizeInput,
  validateSchema(ChangePasswordSchema),
  authController.changePassword
);

router.get('/me', authController.me);

export default router;
