import { api } from '@/utils/api';
import { API_ENDPOINTS } from '@/utils/config';
import { 
  LoginRequest, 
  CreateUserRequest, 
  AuthResponse, 
  ChangePasswordRequest,
  UpdateUserRequest,
  User
} from '@/types';

export const authService = {
  // Login user
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>(API_ENDPOINTS.AUTH.LOGIN, credentials);
    return response.data!;
  },

  // Register new user
  register: async (userData: CreateUserRequest): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>(API_ENDPOINTS.AUTH.REGISTER, userData);
    return response.data!;
  },

  // Refresh token
  refreshToken: async (refreshToken: string): Promise<{ token: string; refreshToken: string }> => {
    const response = await api.post<{ token: string; refreshToken: string }>(
      API_ENDPOINTS.AUTH.REFRESH,
      { refreshToken }
    );
    return response.data!;
  },

  // Logout user
  logout: async (): Promise<void> => {
    await api.post(API_ENDPOINTS.AUTH.LOGOUT);
  },

  // Get user profile
  getProfile: async (): Promise<User> => {
    const response = await api.get<User>(API_ENDPOINTS.AUTH.PROFILE);
    return response.data!;
  },

  // Update user profile
  updateProfile: async (userData: UpdateUserRequest): Promise<User> => {
    const response = await api.put<User>(API_ENDPOINTS.AUTH.PROFILE, userData);
    return response.data!;
  },

  // Change password
  changePassword: async (passwordData: ChangePasswordRequest): Promise<void> => {
    await api.post(API_ENDPOINTS.AUTH.CHANGE_PASSWORD, passwordData);
  },

  // Get current user info
  me: async (): Promise<User> => {
    const response = await api.get<User>(API_ENDPOINTS.AUTH.ME);
    return response.data!;
  }
};
