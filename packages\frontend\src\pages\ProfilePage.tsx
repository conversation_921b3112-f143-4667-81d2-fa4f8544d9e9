import React from 'react';
import { Card, Typography } from 'antd';
import { Helmet } from 'react-helmet-async';
import { config } from '@/utils/config';

const { Title } = Typography;

const ProfilePage: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Profile - {config.appName}</title>
        <meta name="description" content="Manage your profile and account settings" />
      </Helmet>

      <div>
        <Title level={2} style={{ marginBottom: 24 }}>
          Profile
        </Title>

        <Card>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <Title level={3} type="secondary">
              Profile Management Coming Soon
            </Title>
            <p>User profile editing and settings will be implemented here.</p>
          </div>
        </Card>
      </div>
    </>
  );
};

export default ProfilePage;
