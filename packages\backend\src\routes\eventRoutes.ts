import { Router } from 'express';
import { eventController } from '../controllers/eventController';
import { 
  authenticate, 
  optionalAuth,
  canCreateEvents, 
  canEditEvents, 
  canDeleteEvents 
} from '../middleware/auth';
import { 
  validateSchema, 
  validateObjectId, 
  validatePagination,
  validateDateRange,
  sanitizeInput 
} from '../middleware/validation';
import { CreateEventSchema, UpdateEventSchema } from '@gbf-calendar/shared';

const router = Router();

// Public routes (with optional authentication)
router.get('/', 
  optionalAuth,
  validatePagination,
  validateDateRange,
  eventController.getEvents
);

router.get('/upcoming', 
  optionalAuth,
  eventController.getUpcomingEvents
);

router.get('/search', 
  optionalAuth,
  eventController.searchEvents
);

router.get('/calendar', 
  optionalAuth,
  validateDateRange,
  eventController.getEventsByDateRange
);

router.get('/stats', 
  optionalAuth,
  eventController.getEventStats
);

router.get('/:id', 
  optionalAuth,
  validateObjectId('id'),
  eventController.getEvent
);

// Protected routes
router.use(authenticate); // All routes below require authentication

// Create event (requires editor or admin role)
router.post('/', 
  canCreateEvents,
  sanitizeInput,
  validateSchema(CreateEventSchema),
  eventController.createEvent
);

// Update event (requires editor or admin role)
router.put('/:id', 
  canEditEvents,
  validateObjectId('id'),
  sanitizeInput,
  validateSchema(UpdateEventSchema),
  eventController.updateEvent
);

// Delete event (requires admin role)
router.delete('/:id', 
  canDeleteEvents,
  validateObjectId('id'),
  eventController.deleteEvent
);

export default router;
