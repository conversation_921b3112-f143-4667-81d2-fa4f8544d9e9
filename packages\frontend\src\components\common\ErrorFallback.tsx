import React from 'react';
import { Result, Button } from 'antd';
import { FallbackProps } from 'react-error-boundary';

const ErrorFallback: React.FC<FallbackProps> = ({ error, resetErrorBoundary }) => {
  return (
    <Result
      status="500"
      title="Something went wrong"
      subTitle={error?.message || 'An unexpected error occurred'}
      extra={
        <Button type="primary" onClick={resetErrorBoundary}>
          Try Again
        </Button>
      }
    />
  );
};

export default ErrorFallback;
