import { z } from 'zod';

// User roles for access control
export enum UserRole {
  ADMIN = 'admin',           // Full access - can create, edit, delete events
  EDITOR = 'editor',         // Can create and edit events
  VIEWER = 'viewer'          // Can only view events
}

// User status
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

// Zod schemas for validation
export const UserSchema = z.object({
  _id: z.string().optional(),
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  role: z.nativeEnum(UserRole).default(UserRole.VIEWER),
  status: z.nativeEnum(UserStatus).default(UserStatus.ACTIVE),
  department: z.string().optional(),
  position: z.string().optional(),
  phone: z.string().optional(),
  avatar: z.string().optional(),
  lastLogin: z.date().optional(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
  preferences: z.object({
    emailNotifications: z.boolean().default(true),
    defaultCalendarView: z.enum(['month', 'week', 'day']).default('month'),
    timezone: z.string().default('UTC')
  }).default({})
});

export const CreateUserSchema = UserSchema.omit({
  _id: true,
  createdAt: true,
  updatedAt: true,
  lastLogin: true
});

export const UpdateUserSchema = UserSchema.partial().omit({
  _id: true,
  createdAt: true,
  password: true // Password updates handled separately
});

export const LoginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required')
});

export const ChangePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
  confirmPassword: z.string().min(1, 'Password confirmation is required')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

// TypeScript types derived from Zod schemas
export type User = z.infer<typeof UserSchema>;
export type CreateUserRequest = z.infer<typeof CreateUserSchema>;
export type UpdateUserRequest = z.infer<typeof UpdateUserSchema>;
export type LoginRequest = z.infer<typeof LoginSchema>;
export type ChangePasswordRequest = z.infer<typeof ChangePasswordSchema>;

// Auth response types
export interface AuthResponse {
  user: Omit<User, 'password'>;
  token: string;
  refreshToken: string;
}

export interface TokenPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}

// User profile (without sensitive data)
export type UserProfile = Omit<User, 'password'>;

// User permissions helper
export const UserPermissions = {
  canCreateEvents: (role: UserRole): boolean => {
    return [UserRole.ADMIN, UserRole.EDITOR].includes(role);
  },
  
  canEditEvents: (role: UserRole): boolean => {
    return [UserRole.ADMIN, UserRole.EDITOR].includes(role);
  },
  
  canDeleteEvents: (role: UserRole): boolean => {
    return role === UserRole.ADMIN;
  },
  
  canManageUsers: (role: UserRole): boolean => {
    return role === UserRole.ADMIN;
  },
  
  canViewEvents: (role: UserRole): boolean => {
    return Object.values(UserRole).includes(role);
  }
};
