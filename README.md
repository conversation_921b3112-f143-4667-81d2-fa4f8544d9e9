# GBF Events Calendar

A digital calendar application for managing GBF activities with comprehensive event management features.

## Features

- 📅 **Digital Calendar Interface** - Clean, intuitive calendar view for all GBF activities
- 🔐 **Role-based Access Control** - Edit access for administrators, view access for all members
- 📝 **Comprehensive Event Details** - Title, agenda, location, SPOC, and more
- 🔍 **Advanced Search & Filtering** - Sort and search by month, event type, location, etc.
- 🌐 **Online Hosting** - Accessible from anywhere with internet connection
- 📱 **Responsive Design** - Works seamlessly on desktop, tablet, and mobile devices

## Tech Stack

### Frontend

- **React.js** - Modern UI framework
- **TanStack Query** - Server state management
- **Styled Components** - CSS-in-JS styling
- **Ant Design** - UI component library
- **Zustand** - Client state management

### Backend

- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **JWT** - Authentication

## Project Structure

```
gbf-events-calendar/
├── packages/
│   ├── frontend/          # React.js application
│   ├── backend/           # Node.js API server
│   └── shared/            # Shared utilities and types
├── package.json           # Root package configuration
└── README.md             # This file
```

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm (v9 or higher)
- MongoDB (local or cloud instance)

### Quick Start with Docker (Recommended)

1. Clone the repository:

```bash
git clone <repository-url>
cd gbf-events-calendar
```

2. Start the application:

```bash
docker-compose up -d
```

3. Access the application:

- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- Default admin login: <EMAIL> / admin123

### Manual Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd gbf-events-calendar
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
# Copy environment templates
cp packages/backend/.env.example packages/backend/.env
cp packages/frontend/.env.example packages/frontend/.env
```

4. Update environment files with your configuration

5. Start MongoDB (if running locally)

6. Start the development servers:

```bash
npm run dev
```

This will start both the frontend (http://localhost:3000) and backend (http://localhost:5000) servers.

### Available Scripts

- `npm run dev` - Start both frontend and backend in development mode
- `npm run dev:frontend` - Start only the frontend development server
- `npm run dev:backend` - Start only the backend development server
- `npm run build` - Build both applications for production
- `npm run test` - Run tests for all packages
- `npm run lint` - Run linting for all packages

## Environment Configuration

### Backend (.env)

```
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/gbf-calendar
JWT_SECRET=your-jwt-secret-key
CORS_ORIGIN=http://localhost:3000
```

### Frontend (.env)

```
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_APP_NAME=GBF Events Calendar
```

## Features Implemented

### ✅ Core Features

- **Monorepo Structure** - Well-organized codebase with shared utilities
- **Authentication & Authorization** - JWT-based auth with role-based access control
- **User Management** - Admin, Editor, and Viewer roles
- **Event Management** - CRUD operations for events
- **RESTful API** - Comprehensive backend API with validation
- **Responsive UI** - Modern React frontend with Ant Design
- **State Management** - Zustand for client state, TanStack Query for server state
- **Type Safety** - Full TypeScript implementation with shared types
- **Database** - MongoDB with proper indexing and validation

### 🚧 Planned Features

- **Calendar View** - Interactive calendar with react-big-calendar
- **Advanced Filtering** - Search and filter events by multiple criteria
- **File Uploads** - Attach documents to events
- **Email Notifications** - Event reminders and updates
- **Recurring Events** - Support for repeating events
- **Event Registration** - RSVP functionality for events

## API Documentation

The backend provides a RESTful API with the following endpoints:

### Authentication

- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/refresh` - Refresh JWT token
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Events

- `GET /api/events` - List events with filtering and pagination
- `POST /api/events` - Create new event (requires editor/admin role)
- `GET /api/events/:id` - Get event by ID
- `PUT /api/events/:id` - Update event (requires editor/admin role)
- `DELETE /api/events/:id` - Delete event (requires admin role)
- `GET /api/events/calendar` - Get events for calendar view
- `GET /api/events/upcoming` - Get upcoming events

## Deployment

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
