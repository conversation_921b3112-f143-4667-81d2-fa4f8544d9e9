version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: gbf-calendar-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: gbf-calendar
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - gbf-calendar-network

  # Backend API
  backend:
    build:
      context: .
      dockerfile: packages/backend/Dockerfile
    container_name: gbf-calendar-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: ***********************************************************************
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production-please-make-it-long
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-change-this-in-production-please-make-it-long
      JWT_EXPIRES_IN: 24h
      JWT_REFRESH_EXPIRES_IN: 7d
      CORS_ORIGIN: http://localhost:3000
      ADMIN_EMAIL: <EMAIL>
      ADMIN_PASSWORD: admin123
      ADMIN_FIRST_NAME: Admin
      ADMIN_LAST_NAME: User
    ports:
      - "5000:5000"
    depends_on:
      - mongodb
    volumes:
      - ./packages/backend/uploads:/app/uploads
      - ./packages/backend/logs:/app/logs
    networks:
      - gbf-calendar-network

  # Frontend Application
  frontend:
    build:
      context: .
      dockerfile: packages/frontend/Dockerfile
    container_name: gbf-calendar-frontend
    restart: unless-stopped
    environment:
      VITE_API_URL: http://localhost:5000/api
      VITE_APP_NAME: GBF Events Calendar
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - gbf-calendar-network

volumes:
  mongodb_data:
    driver: local

networks:
  gbf-calendar-network:
    driver: bridge
