import { create } from 'zustand';
import { CalendarView, FilterState, EventType, EventStatus, EventPriority } from '@/types';

interface CalendarState {
  view: CalendarView;
  filters: FilterState;
  selectedEventId: string | null;
  isEventModalOpen: boolean;
  isFilterPanelOpen: boolean;
}

interface CalendarActions {
  setView: (view: CalendarView['view']) => void;
  setDate: (date: Date) => void;
  setFilters: (filters: Partial<FilterState>) => void;
  resetFilters: () => void;
  setSelectedEvent: (eventId: string | null) => void;
  setEventModalOpen: (open: boolean) => void;
  setFilterPanelOpen: (open: boolean) => void;
  navigateCalendar: (direction: 'prev' | 'next' | 'today') => void;
}

type CalendarStore = CalendarState & CalendarActions;

const initialFilters: FilterState = {
  dateRange: {},
  types: [],
  statuses: [],
  priorities: [],
  tags: [],
  search: '',
  location: ''
};

const initialState: CalendarState = {
  view: {
    view: 'month',
    date: new Date()
  },
  filters: initialFilters,
  selectedEventId: null,
  isEventModalOpen: false,
  isFilterPanelOpen: false
};

export const useCalendarStore = create<CalendarStore>((set, get) => ({
  ...initialState,

  setView: (view) => {
    set((state) => ({
      view: { ...state.view, view }
    }));
  },

  setDate: (date) => {
    set((state) => ({
      view: { ...state.view, date }
    }));
  },

  setFilters: (newFilters) => {
    set((state) => ({
      filters: { ...state.filters, ...newFilters }
    }));
  },

  resetFilters: () => {
    set({ filters: initialFilters });
  },

  setSelectedEvent: (eventId) => {
    set({ selectedEventId: eventId });
  },

  setEventModalOpen: (open) => {
    set({ isEventModalOpen: open });
    if (!open) {
      set({ selectedEventId: null });
    }
  },

  setFilterPanelOpen: (open) => {
    set({ isFilterPanelOpen: open });
  },

  navigateCalendar: (direction) => {
    const { view } = get();
    const currentDate = new Date(view.date);

    switch (direction) {
      case 'prev':
        if (view.view === 'month') {
          currentDate.setMonth(currentDate.getMonth() - 1);
        } else if (view.view === 'week') {
          currentDate.setDate(currentDate.getDate() - 7);
        } else if (view.view === 'day') {
          currentDate.setDate(currentDate.getDate() - 1);
        }
        break;
      case 'next':
        if (view.view === 'month') {
          currentDate.setMonth(currentDate.getMonth() + 1);
        } else if (view.view === 'week') {
          currentDate.setDate(currentDate.getDate() + 7);
        } else if (view.view === 'day') {
          currentDate.setDate(currentDate.getDate() + 1);
        }
        break;
      case 'today':
        currentDate.setTime(new Date().getTime());
        break;
    }

    set((state) => ({
      view: { ...state.view, date: currentDate }
    }));
  }
}));

// Selectors
export const useCalendarView = () => useCalendarStore((state) => state.view);
export const useCalendarFilters = () => useCalendarStore((state) => state.filters);
export const useSelectedEvent = () => useCalendarStore((state) => state.selectedEventId);
export const useEventModal = () => useCalendarStore((state) => ({
  isOpen: state.isEventModalOpen,
  setOpen: state.setEventModalOpen
}));
export const useFilterPanel = () => useCalendarStore((state) => ({
  isOpen: state.isFilterPanelOpen,
  setOpen: state.setFilterPanelOpen
}));

// Helper functions
export const getActiveFiltersCount = (): number => {
  const filters = useCalendarStore.getState().filters;
  let count = 0;

  if (filters.dateRange.start || filters.dateRange.end) count++;
  if (filters.types.length > 0) count++;
  if (filters.statuses.length > 0) count++;
  if (filters.priorities.length > 0) count++;
  if (filters.tags.length > 0) count++;
  if (filters.search.trim()) count++;
  if (filters.location.trim()) count++;

  return count;
};

export const hasActiveFilters = (): boolean => {
  return getActiveFiltersCount() > 0;
};
