import React from 'react';
import { Card, Typography } from 'antd';
import { Helmet } from 'react-helmet-async';
import { config } from '@/utils/config';

const { Title } = Typography;

const EventsPage: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Events - {config.appName}</title>
        <meta name="description" content="Manage and browse all events" />
      </Helmet>

      <div>
        <Title level={2} style={{ marginBottom: 24 }}>
          Events
        </Title>

        <Card>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <Title level={3} type="secondary">
              Events Management Coming Soon
            </Title>
            <p>The events list, filters, and management features will be implemented here.</p>
          </div>
        </Card>
      </div>
    </>
  );
};

export default EventsPage;
