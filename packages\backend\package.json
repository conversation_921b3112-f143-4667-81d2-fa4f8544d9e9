{"name": "@gbf-calendar/backend", "version": "1.0.0", "description": "Backend API server for GBF Events Calendar", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "clean": "rm -rf dist"}, "dependencies": {"@gbf-calendar/shared": "^1.0.0", "express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "dotenv": "^16.3.1", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "nodemailer": "^6.9.7", "cron": "^3.1.6", "express-async-errors": "^3.1.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/nodemailer": "^6.4.14", "@types/node": "^20.10.0", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "nodemon": "^3.0.2", "supertest": "^6.3.3", "typescript": "^5.3.0", "ts-node": "^10.9.1"}, "engines": {"node": ">=18.0.0"}}