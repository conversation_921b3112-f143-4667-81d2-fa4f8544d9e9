// Re-export shared types
export * from '@gbf-calendar/shared';

// Frontend-specific types
export interface AppConfig {
  apiUrl: string;
  appName: string;
  environment: string;
  enableDevTools: boolean;
  enableQueryDevtools: boolean;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface CalendarView {
  view: 'month' | 'week' | 'day' | 'agenda';
  date: Date;
}

export interface EventFormData {
  title: string;
  description?: string;
  agenda?: string;
  startDate: Date;
  endDate: Date;
  location?: string;
  venue?: string;
  spoc?: string;
  contactEmail?: string;
  contactPhone?: string;
  type: EventType;
  priority: EventPriority;
  status: EventStatus;
  maxAttendees?: number;
  registrationRequired: boolean;
  registrationDeadline?: Date;
  tags: string[];
  isRecurring: boolean;
  recurrencePattern?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number;
    endDate?: Date;
    daysOfWeek?: number[];
  };
}

export interface FilterState {
  dateRange: {
    start?: Date;
    end?: Date;
  };
  types: EventType[];
  statuses: EventStatus[];
  priorities: EventPriority[];
  tags: string[];
  search: string;
  location: string;
}

export interface NotificationState {
  notifications: Notification[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: Date;
}

// Component props types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingProps extends BaseComponentProps {
  size?: 'small' | 'default' | 'large';
  spinning?: boolean;
  tip?: string;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

// API response types for frontend
export interface ApiHookOptions {
  enabled?: boolean;
  refetchOnWindowFocus?: boolean;
  retry?: boolean | number;
  staleTime?: number;
  cacheTime?: number;
}

export interface MutationOptions<TData = any, TError = any, TVariables = any> {
  onSuccess?: (data: TData, variables: TVariables) => void;
  onError?: (error: TError, variables: TVariables) => void;
  onSettled?: (data: TData | undefined, error: TError | null, variables: TVariables) => void;
}
