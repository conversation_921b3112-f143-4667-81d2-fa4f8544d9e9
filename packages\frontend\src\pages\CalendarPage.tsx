import React from 'react';
import { Card, Typography } from 'antd';
import { Helmet } from 'react-helmet-async';
import { config } from '@/utils/config';

const { Title } = Typography;

const CalendarPage: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Calendar - {config.appName}</title>
        <meta name="description" content="View and manage events in calendar format" />
      </Helmet>

      <div>
        <Title level={2} style={{ marginBottom: 24 }}>
          Calendar
        </Title>

        <Card>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <Title level={3} type="secondary">
              Calendar Component Coming Soon
            </Title>
            <p>The calendar view will be implemented with react-big-calendar integration.</p>
          </div>
        </Card>
      </div>
    </>
  );
};

export default CalendarPage;
