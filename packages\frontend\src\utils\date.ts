// Re-export shared date utilities
export * from '@gbf-calendar/shared';

import { format, parseISO, isValid } from 'date-fns';

// Additional frontend-specific date utilities

// Format date for display in different contexts
export const formatDateForDisplay = (
  date: Date | string,
  formatType: 'short' | 'long' | 'time' | 'datetime' | 'input' = 'short'
): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  
  if (!isValid(dateObj)) {
    return 'Invalid Date';
  }

  switch (formatType) {
    case 'short':
      return format(dateObj, 'MMM dd, yyyy');
    case 'long':
      return format(dateObj, 'EEEE, MMMM dd, yyyy');
    case 'time':
      return format(dateObj, 'HH:mm');
    case 'datetime':
      return format(dateObj, 'MMM dd, yyyy HH:mm');
    case 'input':
      return format(dateObj, 'yyyy-MM-dd');
    default:
      return format(dateObj, 'MMM dd, yyyy');
  }
};

// Format date for form inputs
export const formatDateForInput = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  
  if (!isValid(dateObj)) {
    return '';
  }

  return format(dateObj, 'yyyy-MM-dd');
};

// Format time for form inputs
export const formatTimeForInput = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  
  if (!isValid(dateObj)) {
    return '';
  }

  return format(dateObj, 'HH:mm');
};

// Combine date and time strings into a Date object
export const combineDateAndTime = (dateStr: string, timeStr: string): Date => {
  const date = new Date(dateStr);
  const [hours, minutes] = timeStr.split(':').map(Number);
  
  date.setHours(hours, minutes, 0, 0);
  return date;
};

// Get date range for calendar views
export const getCalendarDateRange = (date: Date, view: 'month' | 'week' | 'day') => {
  const start = new Date(date);
  const end = new Date(date);

  switch (view) {
    case 'month':
      start.setDate(1);
      start.setHours(0, 0, 0, 0);
      end.setMonth(end.getMonth() + 1, 0);
      end.setHours(23, 59, 59, 999);
      break;
    case 'week':
      const dayOfWeek = start.getDay();
      start.setDate(start.getDate() - dayOfWeek);
      start.setHours(0, 0, 0, 0);
      end.setDate(start.getDate() + 6);
      end.setHours(23, 59, 59, 999);
      break;
    case 'day':
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      break;
  }

  return { start, end };
};

// Check if a date is within business hours
export const isBusinessHours = (date: Date): boolean => {
  const hour = date.getHours();
  const day = date.getDay();
  
  // Monday to Friday, 9 AM to 6 PM
  return day >= 1 && day <= 5 && hour >= 9 && hour < 18;
};

// Get next business day
export const getNextBusinessDay = (date: Date): Date => {
  const nextDay = new Date(date);
  nextDay.setDate(nextDay.getDate() + 1);
  
  // Skip weekends
  while (nextDay.getDay() === 0 || nextDay.getDay() === 6) {
    nextDay.setDate(nextDay.getDate() + 1);
  }
  
  return nextDay;
};

// Calculate event duration in human-readable format
export const getEventDuration = (startDate: Date, endDate: Date): string => {
  const diffMs = endDate.getTime() - startDate.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffDays > 0) {
    const remainingHours = diffHours % 24;
    if (remainingHours > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ${remainingHours} hour${remainingHours > 1 ? 's' : ''}`;
    }
    return `${diffDays} day${diffDays > 1 ? 's' : ''}`;
  }

  if (diffHours > 0) {
    const remainingMinutes = diffMinutes % 60;
    if (remainingMinutes > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}`;
    }
    return `${diffHours} hour${diffHours > 1 ? 's' : ''}`;
  }

  return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
};

// Get time until event starts
export const getTimeUntilEvent = (eventDate: Date): string => {
  const now = new Date();
  const diffMs = eventDate.getTime() - now.getTime();

  if (diffMs <= 0) {
    return 'Event has started';
  }

  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffDays > 0) {
    return `Starts in ${diffDays} day${diffDays > 1 ? 's' : ''}`;
  }

  if (diffHours > 0) {
    return `Starts in ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
  }

  if (diffMinutes > 0) {
    return `Starts in ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
  }

  return 'Starting soon';
};
