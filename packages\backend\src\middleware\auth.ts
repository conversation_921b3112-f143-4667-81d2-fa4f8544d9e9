import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { User, UserDocument } from '../models/User';
import { UserRole, TokenPayload } from '@gbf-calendar/shared';
import { config } from '../config/environment';
import { logger } from '../utils/logger';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: UserDocument;
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user: UserDocument;
}

// Middleware to authenticate JWT token
export const authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: 'Access token is required'
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    try {
      const decoded = jwt.verify(token, config.JWT_SECRET) as TokenPayload;
      
      // Find user by ID
      const user = await User.findById(decoded.userId);
      
      if (!user) {
        res.status(401).json({
          success: false,
          error: 'User not found'
        });
        return;
      }

      // Check if user is active
      if (user.status !== 'active') {
        res.status(401).json({
          success: false,
          error: 'User account is not active'
        });
        return;
      }

      // Attach user to request
      req.user = user;
      next();
    } catch (jwtError) {
      logger.warn('JWT verification failed', { error: jwtError, token: token.substring(0, 20) + '...' });
      res.status(401).json({
        success: false,
        error: 'Invalid or expired token'
      });
      return;
    }
  } catch (error) {
    logger.error('Authentication middleware error', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
};

// Middleware to check if user has required role
export const authorize = (...roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    if (!roles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        error: 'Insufficient permissions'
      });
      return;
    }

    next();
  };
};

// Middleware to check if user can create events
export const canCreateEvents = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
    return;
  }

  const allowedRoles = [UserRole.ADMIN, UserRole.EDITOR];
  if (!allowedRoles.includes(req.user.role)) {
    res.status(403).json({
      success: false,
      error: 'You do not have permission to create events'
    });
    return;
  }

  next();
};

// Middleware to check if user can edit events
export const canEditEvents = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
    return;
  }

  const allowedRoles = [UserRole.ADMIN, UserRole.EDITOR];
  if (!allowedRoles.includes(req.user.role)) {
    res.status(403).json({
      success: false,
      error: 'You do not have permission to edit events'
    });
    return;
  }

  next();
};

// Middleware to check if user can delete events
export const canDeleteEvents = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
    return;
  }

  if (req.user.role !== UserRole.ADMIN) {
    res.status(403).json({
      success: false,
      error: 'You do not have permission to delete events'
    });
    return;
  }

  next();
};

// Middleware to check if user can manage users
export const canManageUsers = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
    return;
  }

  if (req.user.role !== UserRole.ADMIN) {
    res.status(403).json({
      success: false,
      error: 'You do not have permission to manage users'
    });
    return;
  }

  next();
};

// Optional authentication - doesn't fail if no token provided
export const optionalAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No token provided, continue without authentication
      next();
      return;
    }

    const token = authHeader.substring(7);

    try {
      const decoded = jwt.verify(token, config.JWT_SECRET) as TokenPayload;
      const user = await User.findById(decoded.userId);
      
      if (user && user.status === 'active') {
        req.user = user;
      }
    } catch (jwtError) {
      // Invalid token, but don't fail - just continue without user
      logger.debug('Optional auth failed', { error: jwtError });
    }

    next();
  } catch (error) {
    logger.error('Optional authentication middleware error', error);
    next(); // Continue even if there's an error
  }
};
