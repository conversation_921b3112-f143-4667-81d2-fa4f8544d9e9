{"name": "@gbf-calendar/frontend", "version": "1.0.0", "description": "Frontend React application for GBF Events Calendar", "private": true, "dependencies": {"@gbf-calendar/shared": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "zustand": "^4.4.7", "styled-components": "^6.1.1", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "axios": "^1.6.2", "date-fns": "^2.30.0", "react-big-calendar": "^1.8.5", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-helmet-async": "^1.3.0", "react-error-boundary": "^4.0.11"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-big-calendar": "^1.8.9", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.3.0", "vite": "^5.0.0", "vite-tsconfig-paths": "^4.2.1"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0.0"}}