import { AppConfig } from '@/types';

// Get environment variables with defaults
const getEnvVar = (name: string, defaultValue: string = ''): string => {
  return import.meta.env[name] || defaultValue;
};

const getBooleanEnvVar = (name: string, defaultValue: boolean = false): boolean => {
  const value = import.meta.env[name];
  if (value === undefined) return defaultValue;
  return value === 'true' || value === '1';
};

// Application configuration
export const config: AppConfig = {
  apiUrl: getEnvVar('VITE_API_URL', 'http://localhost:5000/api'),
  appName: getEnvVar('VITE_APP_NAME', 'GBF Events Calendar'),
  environment: getEnvVar('VITE_NODE_ENV', 'development'),
  enableDevTools: getBooleanEnvVar('VITE_ENABLE_DEV_TOOLS', true),
  enableQueryDevtools: getBooleanEnvVar('VITE_ENABLE_QUERY_DEVTOOLS', true)
};

// Validate required configuration
export const validateConfig = (): void => {
  if (!config.apiUrl) {
    throw new Error('VITE_API_URL is required');
  }

  // Validate API URL format
  try {
    new URL(config.apiUrl);
  } catch {
    throw new Error('VITE_API_URL must be a valid URL');
  }
};

// Helper functions
export const isDevelopment = (): boolean => config.environment === 'development';
export const isProduction = (): boolean => config.environment === 'production';

// API endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    LOGOUT: '/auth/logout',
    PROFILE: '/auth/profile',
    CHANGE_PASSWORD: '/auth/change-password',
    ME: '/auth/me'
  },
  
  // Event endpoints
  EVENTS: {
    BASE: '/events',
    BY_ID: (id: string) => `/events/${id}`,
    SEARCH: '/events/search',
    CALENDAR: '/events/calendar',
    UPCOMING: '/events/upcoming',
    STATS: '/events/stats'
  },
  
  // Health check
  HEALTH: '/health'
} as const;
