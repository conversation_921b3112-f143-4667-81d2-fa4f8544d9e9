import { create } from 'zustand';
import { Notification, NotificationState } from '@/types';

interface NotificationActions {
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

type NotificationStore = NotificationState & NotificationActions;

const initialState: NotificationState = {
  notifications: []
};

export const useNotificationStore = create<NotificationStore>((set, get) => ({
  ...initialState,

  addNotification: (notification) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: new Date()
    };

    set((state) => ({
      notifications: [...state.notifications, newNotification]
    }));

    // Auto-remove notification after duration (default 5 seconds)
    const duration = notification.duration || 5000;
    if (duration > 0) {
      setTimeout(() => {
        get().removeNotification(id);
      }, duration);
    }
  },

  removeNotification: (id) => {
    set((state) => ({
      notifications: state.notifications.filter(n => n.id !== id)
    }));
  },

  clearNotifications: () => {
    set({ notifications: [] });
  }
}));

// Selectors
export const useNotifications = () => useNotificationStore((state) => state.notifications);

// Helper functions for common notification types
export const useNotificationActions = () => {
  const { addNotification, removeNotification, clearNotifications } = useNotificationStore();

  return {
    success: (title: string, message: string, duration?: number) => {
      addNotification({ type: 'success', title, message, duration });
    },
    
    error: (title: string, message: string, duration?: number) => {
      addNotification({ type: 'error', title, message, duration: duration || 0 }); // Errors don't auto-dismiss
    },
    
    warning: (title: string, message: string, duration?: number) => {
      addNotification({ type: 'warning', title, message, duration });
    },
    
    info: (title: string, message: string, duration?: number) => {
      addNotification({ type: 'info', title, message, duration });
    },
    
    remove: removeNotification,
    clear: clearNotifications
  };
};
