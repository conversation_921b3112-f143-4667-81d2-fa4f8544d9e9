import { z } from 'zod';

// Event priority levels
export enum EventPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// Event status
export enum EventStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed'
}

// Event types/categories
export enum EventType {
  MEETING = 'meeting',
  WORKSHOP = 'workshop',
  CONFERENCE = 'conference',
  TRAINING = 'training',
  SOCIAL = 'social',
  ANNOUNCEMENT = 'announcement',
  OTHER = 'other'
}

// Zod schemas for validation
export const EventSchema = z.object({
  _id: z.string().optional(),
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().optional(),
  agenda: z.string().optional(),
  startDate: z.date(),
  endDate: z.date(),
  location: z.string().optional(),
  venue: z.string().optional(),
  spoc: z.string().optional(), // Single Point of Contact
  contactEmail: z.string().email().optional(),
  contactPhone: z.string().optional(),
  type: z.nativeEnum(EventType).default(EventType.OTHER),
  priority: z.nativeEnum(EventPriority).default(EventPriority.MEDIUM),
  status: z.nativeEnum(EventStatus).default(EventStatus.DRAFT),
  maxAttendees: z.number().positive().optional(),
  registrationRequired: z.boolean().default(false),
  registrationDeadline: z.date().optional(),
  tags: z.array(z.string()).default([]),
  attachments: z.array(z.object({
    name: z.string(),
    url: z.string(),
    type: z.string()
  })).default([]),
  createdBy: z.string(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
  isRecurring: z.boolean().default(false),
  recurrencePattern: z.object({
    frequency: z.enum(['daily', 'weekly', 'monthly', 'yearly']),
    interval: z.number().positive(),
    endDate: z.date().optional(),
    daysOfWeek: z.array(z.number().min(0).max(6)).optional()
  }).optional()
});

export const CreateEventSchema = EventSchema.omit({
  _id: true,
  createdAt: true,
  updatedAt: true
});

export const UpdateEventSchema = EventSchema.partial().omit({
  _id: true,
  createdBy: true,
  createdAt: true
});

// TypeScript types derived from Zod schemas
export type Event = z.infer<typeof EventSchema>;
export type CreateEventRequest = z.infer<typeof CreateEventSchema>;
export type UpdateEventRequest = z.infer<typeof UpdateEventSchema>;

// Event filter and search types
export interface EventFilters {
  startDate?: Date;
  endDate?: Date;
  type?: EventType[];
  status?: EventStatus[];
  priority?: EventPriority[];
  tags?: string[];
  search?: string;
  createdBy?: string;
  location?: string;
}

export interface EventSearchParams {
  page?: number;
  limit?: number;
  sortBy?: 'startDate' | 'createdAt' | 'title' | 'priority';
  sortOrder?: 'asc' | 'desc';
  filters?: EventFilters;
}

// API Response types
export interface PaginatedEventsResponse {
  events: Event[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
