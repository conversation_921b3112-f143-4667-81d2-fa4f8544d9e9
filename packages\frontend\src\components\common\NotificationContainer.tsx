import React, { useEffect } from 'react';
import { notification } from 'antd';
import { useNotifications, useNotificationActions } from '@/store/notificationStore';

const NotificationContainer: React.FC = () => {
  const notifications = useNotifications();
  const { remove } = useNotificationActions();

  useEffect(() => {
    notifications.forEach((notif) => {
      notification[notif.type]({
        key: notif.id,
        message: notif.title,
        description: notif.message,
        duration: notif.duration ? notif.duration / 1000 : 4.5,
        onClose: () => remove(notif.id)
      });
    });
  }, [notifications, remove]);

  return null;
};

export default NotificationContainer;
