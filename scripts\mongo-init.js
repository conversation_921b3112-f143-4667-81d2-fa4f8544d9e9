// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the gbf-calendar database
db = db.getSiblingDB('gbf-calendar');

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'password', 'firstName', 'lastName', 'role'],
      properties: {
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
          description: 'must be a valid email address'
        },
        password: {
          bsonType: 'string',
          minLength: 8,
          description: 'must be a string with at least 8 characters'
        },
        firstName: {
          bsonType: 'string',
          minLength: 1,
          description: 'must be a non-empty string'
        },
        lastName: {
          bsonType: 'string',
          minLength: 1,
          description: 'must be a non-empty string'
        },
        role: {
          bsonType: 'string',
          enum: ['admin', 'editor', 'viewer'],
          description: 'must be one of admin, editor, or viewer'
        },
        status: {
          bsonType: 'string',
          enum: ['active', 'inactive', 'suspended'],
          description: 'must be one of active, inactive, or suspended'
        }
      }
    }
  }
});

db.createCollection('events', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['title', 'startDate', 'endDate', 'createdBy'],
      properties: {
        title: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 200,
          description: 'must be a non-empty string with max 200 characters'
        },
        startDate: {
          bsonType: 'date',
          description: 'must be a valid date'
        },
        endDate: {
          bsonType: 'date',
          description: 'must be a valid date'
        },
        type: {
          bsonType: 'string',
          enum: ['meeting', 'workshop', 'conference', 'training', 'social', 'announcement', 'other'],
          description: 'must be a valid event type'
        },
        priority: {
          bsonType: 'string',
          enum: ['low', 'medium', 'high', 'urgent'],
          description: 'must be a valid priority level'
        },
        status: {
          bsonType: 'string',
          enum: ['draft', 'published', 'cancelled', 'completed'],
          description: 'must be a valid status'
        }
      }
    }
  }
});

// Create indexes for better performance
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ role: 1 });
db.users.createIndex({ status: 1 });
db.users.createIndex({ createdAt: -1 });

db.events.createIndex({ startDate: 1 });
db.events.createIndex({ endDate: 1 });
db.events.createIndex({ status: 1 });
db.events.createIndex({ type: 1 });
db.events.createIndex({ priority: 1 });
db.events.createIndex({ createdBy: 1 });
db.events.createIndex({ tags: 1 });
db.events.createIndex({ title: 'text', description: 'text', agenda: 'text' });
db.events.createIndex({ startDate: 1, endDate: 1 });
db.events.createIndex({ status: 1, startDate: 1 });

print('Database initialization completed successfully!');
