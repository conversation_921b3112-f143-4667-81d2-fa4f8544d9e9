import { config, validateConfig } from './config/environment';
import { database } from './config/database';
import { authService } from './services/authService';
import { logger, logInfo, logError } from './utils/logger';
import { handleUnhandledRejection, handleUncaughtException } from './middleware/errorHandler';
import app from './app';

// Handle uncaught exceptions and unhandled rejections
process.on('uncaughtException', handleUncaughtException);
process.on('unhandledRejection', handleUnhandledRejection);

// Graceful shutdown handler
const gracefulShutdown = async (signal: string) => {
  logInfo(`Received ${signal}. Starting graceful shutdown...`);
  
  try {
    // Close database connection
    await database.disconnect();
    logInfo('Database connection closed');
    
    // Exit process
    process.exit(0);
  } catch (error) {
    logError('Error during graceful shutdown', error as Error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server
const startServer = async () => {
  try {
    // Validate environment configuration
    validateConfig();
    logInfo('Environment configuration validated');

    // Connect to database
    await database.connect();
    logInfo('Database connected successfully');

    // Create admin user if it doesn't exist
    const adminUser = await authService.createAdminUser();
    if (adminUser) {
      logInfo('Admin user created successfully');
    }

    // Start HTTP server
    const server = app.listen(config.PORT, () => {
      logInfo(`Server started on port ${config.PORT}`, {
        environment: config.NODE_ENV,
        port: config.PORT,
        mongoUri: config.MONGODB_URI.replace(/\/\/.*@/, '//***:***@') // Hide credentials in logs
      });
    });

    // Handle server errors
    server.on('error', (error: Error) => {
      logError('Server error', error);
      process.exit(1);
    });

    return server;
  } catch (error) {
    logError('Failed to start server', error as Error);
    process.exit(1);
  }
};

// Start the application
startServer().catch((error) => {
  logError('Startup error', error);
  process.exit(1);
});
