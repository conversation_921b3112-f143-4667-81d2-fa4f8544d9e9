{"name": "gbf-events-calendar", "version": "1.0.0", "description": "Digital calendar for GBF activities with event management", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "npm run dev --workspace=@gbf-calendar/frontend", "dev:backend": "npm run dev --workspace=@gbf-calendar/backend", "build": "npm run build --workspaces", "build:frontend": "npm run build --workspace=@gbf-calendar/frontend", "build:backend": "npm run build --workspace=@gbf-calendar/backend", "start": "npm run start --workspace=@gbf-calendar/backend", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "clean": "npm run clean --workspaces && rm -rf node_modules"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/gbf/events-calendar.git"}, "keywords": ["calendar", "events", "gbf", "react", "nodejs", "mongodb"], "author": "GBF Team", "license": "MIT"}