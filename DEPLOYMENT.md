# Deployment Guide

This guide covers different deployment options for the GBF Events Calendar application.

## Prerequisites

- Node.js 18+ and npm 9+
- MongoDB instance (local or cloud)
- Git

## Environment Setup

### Backend Environment Variables

Create `packages/backend/.env` file:

```env
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb://localhost:27017/gbf-calendar
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-please-make-it-long
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production-please-make-it-long
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
CORS_ORIGIN=https://your-frontend-domain.com
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
ADMIN_FIRST_NAME=Admin
ADMIN_LAST_NAME=User
```

### Frontend Environment Variables

Create `packages/frontend/.env` file:

```env
VITE_API_URL=https://your-backend-domain.com/api
VITE_APP_NAME=GBF Events Calendar
VITE_NODE_ENV=production
```

## Deployment Options

### Option 1: Docker Compose (Recommended for Development)

1. **Clone and setup:**
```bash
git clone <repository-url>
cd gbf-events-calendar
```

2. **Start services:**
```bash
docker-compose up -d
```

3. **Access the application:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- MongoDB: localhost:27017

### Option 2: Manual Deployment

#### Backend Deployment

1. **Install dependencies:**
```bash
cd packages/backend
npm install
```

2. **Build the application:**
```bash
npm run build
```

3. **Start the server:**
```bash
npm start
```

#### Frontend Deployment

1. **Install dependencies:**
```bash
cd packages/frontend
npm install
```

2. **Build for production:**
```bash
npm run build
```

3. **Serve the built files:**
```bash
# Using a static file server
npx serve -s dist -l 3000
```

### Option 3: Cloud Deployment

#### Backend (Node.js hosting platforms)

**Heroku:**
1. Create a new Heroku app
2. Set environment variables in Heroku dashboard
3. Connect to MongoDB Atlas
4. Deploy using Git or GitHub integration

**Railway/Render/DigitalOcean:**
1. Connect your repository
2. Set environment variables
3. Configure build and start commands
4. Deploy

#### Frontend (Static hosting platforms)

**Vercel:**
1. Connect your repository
2. Set build command: `cd packages/frontend && npm run build`
3. Set output directory: `packages/frontend/dist`
4. Set environment variables
5. Deploy

**Netlify:**
1. Connect your repository
2. Set build command: `cd packages/frontend && npm run build`
3. Set publish directory: `packages/frontend/dist`
4. Set environment variables
5. Deploy

#### Database

**MongoDB Atlas (Recommended):**
1. Create a MongoDB Atlas account
2. Create a new cluster
3. Create a database user
4. Whitelist IP addresses
5. Get connection string and update MONGODB_URI

## Production Considerations

### Security

1. **Environment Variables:**
   - Use strong, unique JWT secrets (32+ characters)
   - Never commit .env files to version control
   - Use different secrets for different environments

2. **CORS Configuration:**
   - Set CORS_ORIGIN to your frontend domain
   - Don't use wildcards (*) in production

3. **HTTPS:**
   - Always use HTTPS in production
   - Update API URLs to use https://

### Performance

1. **Database:**
   - Ensure proper indexes are created
   - Monitor database performance
   - Set up database backups

2. **Caching:**
   - Implement Redis for session storage (optional)
   - Use CDN for static assets

3. **Monitoring:**
   - Set up application monitoring (e.g., Sentry)
   - Monitor server resources
   - Set up log aggregation

### Scaling

1. **Horizontal Scaling:**
   - Use load balancers for multiple backend instances
   - Ensure stateless backend design

2. **Database Scaling:**
   - Consider MongoDB replica sets
   - Implement database sharding if needed

## Health Checks

The application includes health check endpoints:

- Backend: `GET /api/health`
- Database: `GET /api/health` (includes DB connectivity check)

## Backup Strategy

1. **Database Backups:**
   - Set up automated MongoDB backups
   - Test restore procedures regularly

2. **Application Backups:**
   - Backup uploaded files
   - Backup configuration files

## Troubleshooting

### Common Issues

1. **CORS Errors:**
   - Check CORS_ORIGIN environment variable
   - Ensure frontend URL matches CORS configuration

2. **Database Connection:**
   - Verify MongoDB URI format
   - Check network connectivity
   - Verify database credentials

3. **JWT Errors:**
   - Ensure JWT secrets are properly set
   - Check token expiration settings

### Logs

- Backend logs: `packages/backend/logs/`
- Check application logs for detailed error information
- Monitor server logs for system-level issues

## Support

For deployment issues:
1. Check the logs for error messages
2. Verify environment variables are set correctly
3. Ensure all dependencies are installed
4. Check network connectivity between services
