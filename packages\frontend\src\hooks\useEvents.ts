import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { eventService } from '@/services';
import { useNotificationActions } from '@/store/notificationStore';
import { 
  Event,
  CreateEventRequest, 
  UpdateEventRequest,
  EventSearchParams,
  MutationOptions
} from '@/types';

// Query keys
export const eventKeys = {
  all: ['events'] as const,
  lists: () => [...eventKeys.all, 'list'] as const,
  list: (params: EventSearchParams) => [...eventKeys.lists(), params] as const,
  details: () => [...eventKeys.all, 'detail'] as const,
  detail: (id: string) => [...eventKeys.details(), id] as const,
  calendar: (startDate: Date, endDate: Date) => [...eventKeys.all, 'calendar', startDate.toISOString(), endDate.toISOString()] as const,
  upcoming: (limit: number) => [...eventKeys.all, 'upcoming', limit] as const,
  search: (query: string, limit: number) => [...eventKeys.all, 'search', query, limit] as const,
  stats: () => [...eventKeys.all, 'stats'] as const
};

// Get events with filters and pagination
export const useEvents = (params: EventSearchParams) => {
  return useQuery({
    queryKey: eventKeys.list(params),
    queryFn: () => eventService.getEvents(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    keepPreviousData: true
  });
};

// Get single event by ID
export const useEvent = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: eventKeys.detail(id),
    queryFn: () => eventService.getEvent(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });
};

// Get events for calendar view
export const useCalendarEvents = (startDate: Date, endDate: Date) => {
  return useQuery({
    queryKey: eventKeys.calendar(startDate, endDate),
    queryFn: () => eventService.getEventsByDateRange(startDate, endDate),
    staleTime: 1 * 60 * 1000, // 1 minute
    enabled: !!startDate && !!endDate
  });
};

// Get upcoming events
export const useUpcomingEvents = (limit: number = 10) => {
  return useQuery({
    queryKey: eventKeys.upcoming(limit),
    queryFn: () => eventService.getUpcomingEvents(limit),
    staleTime: 2 * 60 * 1000 // 2 minutes
  });
};

// Search events
export const useSearchEvents = (query: string, limit: number = 20, enabled: boolean = true) => {
  return useQuery({
    queryKey: eventKeys.search(query, limit),
    queryFn: () => eventService.searchEvents(query, limit),
    enabled: enabled && query.trim().length > 0,
    staleTime: 30 * 1000 // 30 seconds
  });
};

// Get event statistics
export const useEventStats = () => {
  return useQuery({
    queryKey: eventKeys.stats(),
    queryFn: eventService.getEventStats,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });
};

// Create event mutation
export const useCreateEvent = (options?: MutationOptions) => {
  const queryClient = useQueryClient();
  const notifications = useNotificationActions();
  
  return useMutation({
    mutationFn: (eventData: CreateEventRequest) => eventService.createEvent(eventData),
    onSuccess: (data) => {
      // Invalidate and refetch events
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
      queryClient.invalidateQueries({ queryKey: eventKeys.all });
      
      notifications.success('Event Created', `"${data.title}" has been created successfully.`);
      options?.onSuccess?.(data, eventData);
    },
    onError: (error: any) => {
      notifications.error('Creation Failed', error.error || 'Failed to create event');
      options?.onError?.(error, eventData);
    },
    onSettled: options?.onSettled
  });
};

// Update event mutation
export const useUpdateEvent = (options?: MutationOptions) => {
  const queryClient = useQueryClient();
  const notifications = useNotificationActions();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEventRequest }) => 
      eventService.updateEvent(id, data),
    onSuccess: (data) => {
      // Update specific event in cache
      queryClient.setQueryData(eventKeys.detail(data._id!), data);
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
      queryClient.invalidateQueries({ queryKey: eventKeys.all });
      
      notifications.success('Event Updated', `"${data.title}" has been updated successfully.`);
      options?.onSuccess?.(data, { id: data._id!, data });
    },
    onError: (error: any, variables) => {
      notifications.error('Update Failed', error.error || 'Failed to update event');
      options?.onError?.(error, variables);
    },
    onSettled: options?.onSettled
  });
};

// Delete event mutation
export const useDeleteEvent = (options?: MutationOptions) => {
  const queryClient = useQueryClient();
  const notifications = useNotificationActions();
  
  return useMutation({
    mutationFn: (id: string) => eventService.deleteEvent(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: eventKeys.detail(id) });
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: eventKeys.lists() });
      queryClient.invalidateQueries({ queryKey: eventKeys.all });
      
      notifications.success('Event Deleted', 'The event has been deleted successfully.');
      options?.onSuccess?.(_, id);
    },
    onError: (error: any, id) => {
      notifications.error('Deletion Failed', error.error || 'Failed to delete event');
      options?.onError?.(error, id);
    },
    onSettled: options?.onSettled
  });
};
