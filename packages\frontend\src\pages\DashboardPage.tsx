import React from 'react';
import { Row, Col, Card, Statistic, Typography, List, Button, Tag } from 'antd';
import { 
  CalendarOutlined, 
  ClockCircleOutlined, 
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import styled from 'styled-components';

import { useEventStats, useUpcomingEvents } from '@/hooks';
import { formatDateForDisplay, getTimeUntilEvent } from '@/utils/date';
import { eventTypeColors, priorityColors } from '@/styles/theme';
import { config } from '@/utils/config';
import { hasPermission } from '@/store';

const { Title, Text } = Typography;

const StatsCard = styled(Card)`
  .ant-statistic-content {
    color: ${props => props.color || '#1890ff'};
  }
`;

const EventCard = styled(Card)`
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
`;

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { data: stats, isLoading: statsLoading } = useEventStats();
  const { data: upcomingEvents, isLoading: eventsLoading } = useUpcomingEvents(5);

  const canCreateEvents = hasPermission('create');

  const handleCreateEvent = () => {
    navigate('/events?action=create');
  };

  const handleViewEvent = (eventId: string) => {
    navigate(`/events/${eventId}`);
  };

  const handleViewAllEvents = () => {
    navigate('/events');
  };

  const handleViewCalendar = () => {
    navigate('/calendar');
  };

  return (
    <>
      <Helmet>
        <title>Dashboard - {config.appName}</title>
        <meta name="description" content="Overview of your GBF events and activities" />
      </Helmet>

      <div>
        <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            Dashboard
          </Title>
          <div style={{ display: 'flex', gap: 12 }}>
            <Button 
              icon={<CalendarOutlined />} 
              onClick={handleViewCalendar}
            >
              View Calendar
            </Button>
            {canCreateEvents && (
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleCreateEvent}
              >
                Create Event
              </Button>
            )}
          </div>
        </div>

        {/* Statistics Cards */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} lg={6}>
            <StatsCard loading={statsLoading}>
              <Statistic
                title="Total Events"
                value={stats?.total || 0}
                prefix={<CalendarOutlined />}
              />
            </StatsCard>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <StatsCard loading={statsLoading} color="#52c41a">
              <Statistic
                title="Upcoming"
                value={stats?.upcoming || 0}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </StatsCard>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <StatsCard loading={statsLoading} color="#faad14">
              <Statistic
                title="Ongoing"
                value={stats?.ongoing || 0}
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </StatsCard>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <StatsCard loading={statsLoading} color="#1890ff">
              <Statistic
                title="Completed"
                value={stats?.completed || 0}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </StatsCard>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          {/* Upcoming Events */}
          <Col xs={24} lg={16}>
            <Card
              title="Upcoming Events"
              extra={
                <Button 
                  type="link" 
                  icon={<EyeOutlined />}
                  onClick={handleViewAllEvents}
                >
                  View All
                </Button>
              }
              loading={eventsLoading}
            >
              {upcomingEvents && upcomingEvents.length > 0 ? (
                <List
                  dataSource={upcomingEvents}
                  renderItem={(event) => (
                    <List.Item style={{ padding: 0 }}>
                      <EventCard 
                        size="small" 
                        onClick={() => handleViewEvent(event._id!)}
                        style={{ width: '100%' }}
                      >
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <div style={{ flex: 1 }}>
                            <Title level={5} style={{ margin: '0 0 8px 0' }}>
                              {event.title}
                            </Title>
                            <Text type="secondary" style={{ display: 'block', marginBottom: 8 }}>
                              {formatDateForDisplay(event.startDate, 'datetime')}
                            </Text>
                            {event.location && (
                              <Text type="secondary" style={{ display: 'block', marginBottom: 8 }}>
                                📍 {event.location}
                              </Text>
                            )}
                            <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
                              <Tag color={eventTypeColors[event.type]}>
                                {event.type.toUpperCase()}
                              </Tag>
                              <Tag color={priorityColors[event.priority]}>
                                {event.priority.toUpperCase()}
                              </Tag>
                            </div>
                          </div>
                          <div style={{ textAlign: 'right' }}>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              {getTimeUntilEvent(new Date(event.startDate))}
                            </Text>
                          </div>
                        </div>
                      </EventCard>
                    </List.Item>
                  )}
                />
              ) : (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <CalendarOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                  <Text type="secondary" style={{ display: 'block' }}>
                    No upcoming events
                  </Text>
                  {canCreateEvents && (
                    <Button 
                      type="primary" 
                      style={{ marginTop: 16 }}
                      onClick={handleCreateEvent}
                    >
                      Create Your First Event
                    </Button>
                  )}
                </div>
              )}
            </Card>
          </Col>

          {/* Quick Actions */}
          <Col xs={24} lg={8}>
            <Card title="Quick Actions">
              <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                <Button 
                  size="large" 
                  icon={<CalendarOutlined />}
                  onClick={handleViewCalendar}
                  block
                >
                  View Calendar
                </Button>
                <Button 
                  size="large" 
                  icon={<EyeOutlined />}
                  onClick={handleViewAllEvents}
                  block
                >
                  Browse All Events
                </Button>
                {canCreateEvents && (
                  <Button 
                    type="primary"
                    size="large" 
                    icon={<PlusOutlined />}
                    onClick={handleCreateEvent}
                    block
                  >
                    Create New Event
                  </Button>
                )}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </>
  );
};

export default DashboardPage;
