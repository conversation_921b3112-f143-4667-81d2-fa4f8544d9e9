import jwt from 'jsonwebtoken';
import { User, UserDocument } from '../models/User';
import { 
  LoginRequest, 
  CreateUserRequest, 
  AuthResponse, 
  TokenPayload,
  UserRole,
  UserStatus 
} from '@gbf-calendar/shared';
import { config } from '../config/environment';
import { CustomError } from '../middleware/errorHandler';
import { logger, logAuth } from '../utils/logger';

export class AuthService {
  // Generate JWT token
  private generateToken(user: UserDocument): string {
    const payload: Omit<TokenPayload, 'iat' | 'exp'> = {
      userId: user._id,
      email: user.email,
      role: user.role
    };

    return jwt.sign(payload, config.JWT_SECRET, {
      expiresIn: config.JWT_EXPIRES_IN
    });
  }

  // Generate refresh token
  private generateRefreshToken(user: UserDocument): string {
    const payload: Omit<TokenPayload, 'iat' | 'exp'> = {
      userId: user._id,
      email: user.email,
      role: user.role
    };

    return jwt.sign(payload, config.JWT_REFRESH_SECRET, {
      expiresIn: config.JWT_REFRESH_EXPIRES_IN
    });
  }

  // Login user
  async login(loginData: LoginRequest): Promise<AuthResponse> {
    try {
      const { email, password } = loginData;

      // Find user with password
      const user = await User.findOne({ email }).select('+password');
      
      if (!user) {
        logAuth('login', email, false, { reason: 'User not found' });
        throw new CustomError('Invalid email or password', 401);
      }

      // Check if user is active
      if (user.status !== UserStatus.ACTIVE) {
        logAuth('login', email, false, { reason: 'User not active', status: user.status });
        throw new CustomError('Account is not active', 401);
      }

      // Verify password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        logAuth('login', email, false, { reason: 'Invalid password' });
        throw new CustomError('Invalid email or password', 401);
      }

      // Update last login
      user.lastLogin = new Date();
      await user.save();

      // Generate tokens
      const token = this.generateToken(user);
      const refreshToken = this.generateRefreshToken(user);

      logAuth('login', email, true, { userId: user._id, role: user.role });

      return {
        user: user.toJSON(),
        token,
        refreshToken
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      logger.error('Login service error', error);
      throw new CustomError('Login failed', 500);
    }
  }

  // Register new user
  async register(userData: CreateUserRequest): Promise<AuthResponse> {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({ email: userData.email });
      if (existingUser) {
        throw new CustomError('User with this email already exists', 409);
      }

      // Create new user
      const user = new User(userData);
      await user.save();

      // Generate tokens
      const token = this.generateToken(user);
      const refreshToken = this.generateRefreshToken(user);

      logAuth('register', userData.email, true, { userId: user._id, role: user.role });

      return {
        user: user.toJSON(),
        token,
        refreshToken
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      logger.error('Registration service error', error);
      throw new CustomError('Registration failed', 500);
    }
  }

  // Refresh token
  async refreshToken(refreshToken: string): Promise<{ token: string; refreshToken: string }> {
    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, config.JWT_REFRESH_SECRET) as TokenPayload;
      
      // Find user
      const user = await User.findById(decoded.userId);
      if (!user) {
        throw new CustomError('User not found', 401);
      }

      // Check if user is active
      if (user.status !== UserStatus.ACTIVE) {
        throw new CustomError('Account is not active', 401);
      }

      // Generate new tokens
      const newToken = this.generateToken(user);
      const newRefreshToken = this.generateRefreshToken(user);

      return {
        token: newToken,
        refreshToken: newRefreshToken
      };
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new CustomError('Invalid refresh token', 401);
      }
      if (error instanceof CustomError) {
        throw error;
      }
      logger.error('Refresh token service error', error);
      throw new CustomError('Token refresh failed', 500);
    }
  }

  // Change password
  async changePassword(
    userId: string, 
    currentPassword: string, 
    newPassword: string
  ): Promise<void> {
    try {
      // Find user with password
      const user = await User.findById(userId).select('+password');
      if (!user) {
        throw new CustomError('User not found', 404);
      }

      // Verify current password
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        throw new CustomError('Current password is incorrect', 400);
      }

      // Update password
      user.password = newPassword;
      await user.save();

      logAuth('change-password', user.email, true, { userId: user._id });
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      logger.error('Change password service error', error);
      throw new CustomError('Password change failed', 500);
    }
  }

  // Get user profile
  async getProfile(userId: string): Promise<UserDocument> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new CustomError('User not found', 404);
      }
      return user;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      logger.error('Get profile service error', error);
      throw new CustomError('Failed to get user profile', 500);
    }
  }

  // Update user profile
  async updateProfile(userId: string, updateData: Partial<UserDocument>): Promise<UserDocument> {
    try {
      // Remove sensitive fields that shouldn't be updated via this method
      const { password, role, status, ...safeUpdateData } = updateData as any;

      const user = await User.findByIdAndUpdate(
        userId,
        { ...safeUpdateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      );

      if (!user) {
        throw new CustomError('User not found', 404);
      }

      return user;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      logger.error('Update profile service error', error);
      throw new CustomError('Profile update failed', 500);
    }
  }

  // Create admin user (for initial setup)
  async createAdminUser(): Promise<UserDocument | null> {
    try {
      // Check if admin user already exists
      const existingAdmin = await User.findOne({ role: UserRole.ADMIN });
      if (existingAdmin) {
        return null; // Admin already exists
      }

      // Create admin user
      const adminData: CreateUserRequest = {
        email: config.ADMIN_EMAIL,
        password: config.ADMIN_PASSWORD,
        firstName: config.ADMIN_FIRST_NAME,
        lastName: config.ADMIN_LAST_NAME,
        role: UserRole.ADMIN,
        status: UserStatus.ACTIVE
      };

      const admin = new User(adminData);
      await admin.save();

      logger.info('Admin user created successfully', { email: admin.email });
      return admin;
    } catch (error) {
      logger.error('Failed to create admin user', error);
      return null;
    }
  }
}

export const authService = new AuthService();
